# 🚀 Railway Deployment Guide

Panduan lengkap untuk deploy aplikasi Posyandu Digital ke Railway.

## 📋 Prerequisites

1. Akun Railway (daftar di [railway.app](https://railway.app))
2. Repository GitHub yang sudah di-push
3. File konfigurasi sudah disiapkan (railway.json, nixpacks.toml)

## 🗂️ Struktur Deployment

```
Railway Project: posyandu-digital
├── 🗄️ MySQL Database Service
├── 🔧 Backend Service (server/)
└── 🌐 Frontend Service (client/)
```

## 🔧 Step-by-Step Deployment

### 1. Setup Railway Project

1. Login ke [railway.app](https://railway.app)
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose repository: `lansia`
5. Project name: `posyandu-digital`

### 2. Add MySQL Database

1. Click "Add Service" → "Database" → "MySQL"
2. Service name: `mysql-db`
3. Wait for deployment to complete
4. Copy connection details dari Variables tab

### 3. Deploy Backend Service

1. Click "Add Service" → "GitHub Repo"
2. Select repository dan set:
   - **Service name**: `backend`
   - **Root directory**: `server`
   - **Build command**: `npm ci`
   - **Start command**: `npm start`

#### Environment Variables untuk Backend:
```env
DB_HOST=<mysql-host-from-railway>
DB_USER=<mysql-user-from-railway>
DB_PASSWORD=<mysql-password-from-railway>
DB_NAME=<mysql-database-from-railway>
DB_PORT=<mysql-port-from-railway>
PORT=3001
NODE_ENV=production
FRONTEND_URL=<frontend-url-will-be-set-later>
```

### 4. Deploy Frontend Service

1. Click "Add Service" → "GitHub Repo"
2. Select repository dan set:
   - **Service name**: `frontend`
   - **Root directory**: `client`
   - **Build command**: `npm run build`
   - **Start command**: `npm start`

#### Environment Variables untuk Frontend:
```env
NEXT_PUBLIC_API_URL=<backend-url-from-railway>
NEXT_PUBLIC_APP_NAME=Posyandu Digital
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 5. Update Cross-Service URLs

1. Copy backend service URL
2. Update frontend `NEXT_PUBLIC_API_URL`
3. Copy frontend service URL  
4. Update backend `FRONTEND_URL`
5. Redeploy both services

### 6. Import Database Schema

1. Connect ke Railway MySQL menggunakan MySQL client
2. Import file `database_schema.sql`
3. Verify tables dan data

## 🔍 Verification Steps

1. ✅ Database service running
2. ✅ Backend service running (check /health endpoint)
3. ✅ Frontend service running
4. ✅ API calls working between frontend-backend
5. ✅ Database queries working
6. ✅ QR code functionality working

## 🐛 Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Check environment variables
   - Verify MySQL service is running
   - Check connection string format

2. **CORS Errors**
   - Verify FRONTEND_URL in backend
   - Check CORS configuration

3. **Build Failures**
   - Check Node.js version compatibility
   - Verify package.json scripts
   - Check for missing dependencies

4. **Environment Variables Not Loading**
   - Verify variable names (case-sensitive)
   - Check for typos
   - Ensure variables are set in Railway dashboard

## 📞 Support

Jika mengalami masalah:
1. Check Railway logs untuk error details
2. Verify semua environment variables
3. Test API endpoints secara manual
4. Check database connection

## 🎯 Production Checklist

- [ ] All services deployed successfully
- [ ] Environment variables configured
- [ ] Database schema imported
- [ ] Cross-service communication working
- [ ] QR code scanning functional
- [ ] Data persistence working
- [ ] Error handling working
- [ ] Performance acceptable
