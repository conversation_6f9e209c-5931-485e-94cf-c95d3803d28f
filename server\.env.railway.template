# Railway Production Environment Variables Template
# Copy these variables to Railway Dashboard > Backend Service > Variables

# Database Configuration (akan diisi otomatis dari Railway MySQL service)
DB_HOST=mysql.railway.internal
DB_USER=root
DB_PASSWORD=<MYSQL_PASSWORD_FROM_RAILWAY>
DB_NAME=railway
DB_PORT=3306

# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URL (akan diisi setelah frontend service di-deploy)
FRONTEND_URL=<FRONTEND_SERVICE_URL_FROM_RAILWAY>

# Example values setelah deployment:
# DB_HOST=containers-us-west-1.railway.app
# DB_USER=root  
# DB_PASSWORD=abc123xyz789
# DB_NAME=railway
# DB_PORT=6543
# FRONTEND_URL=https://posyandu-frontend-production.up.railway.app
