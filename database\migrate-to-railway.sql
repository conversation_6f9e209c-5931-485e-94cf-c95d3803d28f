-- Railway Database Migration Script
-- Aplikasi Pencatatan Kesehatan Lansia
-- 
-- INSTRUCTIONS:
-- 1. Connect to Railway MySQL using provided credentials
-- 2. Run this script to create tables and insert sample data
-- 3. Verify tables are created successfully

-- Use the Railway database (usually named 'railway')
-- Note: Railway automatically creates the database, so we don't need CREATE DATABASE

-- Drop existing tables if they exist (for clean migration)
DROP TABLE IF EXISTS checkups;
DROP TABLE IF EXISTS profiles;

-- Create profiles table
CREATE TABLE profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255),
    nama VARCHAR(100) NOT NULL,
    usia INT NOT NULL,
    alamat TEXT NOT NULL,
    riwayat_medis TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create checkups table
CREATE TABLE checkups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id INT NOT NULL,
    tekanan_darah VARCHAR(20),
    gula_darah INT,
    tanggal DATETIME DEFAULT CURRENT_TIMESTAMP,
    catatan TEXT,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    INDEX idx_profile_id (profile_id),
    INDEX idx_tanggal (tanggal)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO profiles (email, nama, usia, alamat, riwayat_medis) VALUES
('<EMAIL>', 'Budi Santoso', 65, 'Jl. Merdeka No. 123, Jakarta', 'Hipertensi, Diabetes'),
('<EMAIL>', 'Siti Aminah', 70, 'Jl. Sudirman No. 456, Jakarta', 'Kolesterol tinggi'),
('<EMAIL>', 'Ahmad Rahman', 68, 'Jl. Thamrin No. 789, Jakarta', 'Tidak ada riwayat khusus'),
('<EMAIL>', 'Maria Sari', 72, 'Jl. Gatot Subroto No. 321, Jakarta', 'Osteoporosis'),
('<EMAIL>', 'John Doe', 69, 'Jl. Kuningan No. 654, Jakarta', 'Hipertensi');

-- Insert sample checkup data
INSERT INTO checkups (profile_id, tekanan_darah, gula_darah, catatan) VALUES
(1, '140/90', 180, 'Tekanan darah tinggi, perlu kontrol rutin'),
(1, '130/85', 160, 'Kondisi membaik setelah pengobatan'),
(2, '120/80', 110, 'Kondisi normal, pertahankan pola hidup sehat'),
(3, '125/82', 95, 'Kondisi baik, tidak ada keluhan'),
(2, '135/88', 125, 'Sedikit naik, perlu monitoring'),
(4, '150/95', 200, 'Perlu perhatian khusus, rujuk ke dokter'),
(5, '128/84', 105, 'Dalam batas normal'),
(1, '125/80', 150, 'Kondisi stabil'),
(3, '130/85', 100, 'Pemeriksaan rutin, kondisi baik'),
(4, '145/92', 185, 'Masih perlu monitoring ketat');

-- Verify data insertion
SELECT 'Profiles created:' as info, COUNT(*) as count FROM profiles;
SELECT 'Checkups created:' as info, COUNT(*) as count FROM checkups;

-- Show sample data
SELECT 'Sample profiles:' as info;
SELECT id, nama, usia, email FROM profiles LIMIT 3;

SELECT 'Sample checkups:' as info;
SELECT c.id, p.nama, c.tekanan_darah, c.gula_darah, c.tanggal 
FROM checkups c 
JOIN profiles p ON c.profile_id = p.id 
LIMIT 5;

-- Success message
SELECT 'Database migration completed successfully!' as status;
