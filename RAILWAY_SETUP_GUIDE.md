# 🚂 Railway Project Setup Guide

## 📋 Step-by-Step Railway Deployment

### 🔐 Prerequisites

1. ✅ GitHub account dengan repository `lansia` yang sudah di-push
2. ✅ Railway account (daftar di [railway.app](https://railway.app))
3. ✅ File konfigurasi sudah disiapkan (railway.json, nixpacks.toml)

## 🚀 Phase 1: Create Railway Project

### Step 1: Login ke Railway
1. Buka [railway.app](https://railway.app)
2. Click "Login" dan connect dengan GitHub
3. Authorize Railway untuk access repositories

### Step 2: Create New Project
1. Click "New Project"
2. Select "Deploy from GitHub repo"
3. Choose repository: `lansia` (atau nama repository Anda)
4. Project name: `posyandu-digital`
5. Click "Deploy"

## 🗄️ Phase 2: Setup MySQL Database

### Step 3: Add MySQL Service
1. Di Railway dashboard, click "Add Service"
2. Select "Database" → "MySQL"
3. Service name: `mysql-database`
4. Wait for deployment (biasanya 2-3 menit)

### Step 4: Get Database Credentials
1. Click pada MySQL service
2. Go to "Variables" tab
3. Copy these values (akan digunakan nanti):
   ```
   MYSQL_HOST=containers-us-west-1.railway.app
   MYSQL_USER=root
   MYSQL_PASSWORD=abc123xyz789
   MYSQL_DATABASE=railway
   MYSQL_PORT=6543
   DATABASE_URL=mysql://root:<EMAIL>:6543/railway
   ```

## 🔧 Phase 3: Deploy Backend Service

### Step 5: Add Backend Service
1. Click "Add Service" → "GitHub Repo"
2. Select repository `lansia`
3. Configure service:
   - **Service name**: `backend-api`
   - **Root directory**: `server`
   - **Build command**: `npm ci` (auto-detected)
   - **Start command**: `npm start` (auto-detected)

### Step 6: Configure Backend Environment Variables
Go to Backend service → Variables tab dan add:

```env
# Database Configuration (copy dari MySQL service)
DB_HOST=${{mysql-database.MYSQL_HOST}}
DB_USER=${{mysql-database.MYSQL_USER}}
DB_PASSWORD=${{mysql-database.MYSQL_PASSWORD}}
DB_NAME=${{mysql-database.MYSQL_DATABASE}}
DB_PORT=${{mysql-database.MYSQL_PORT}}

# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URL (akan diupdate setelah frontend deploy)
FRONTEND_URL=https://posyandu-frontend-production.up.railway.app
```

### Step 7: Deploy Backend
1. Click "Deploy" atau wait for auto-deploy
2. Monitor logs untuk memastikan deployment berhasil
3. Copy backend service URL (format: `https://backend-api-production.up.railway.app`)

## 🌐 Phase 4: Deploy Frontend Service

### Step 8: Add Frontend Service
1. Click "Add Service" → "GitHub Repo"
2. Select repository `lansia`
3. Configure service:
   - **Service name**: `frontend-web`
   - **Root directory**: `client`
   - **Build command**: `npm run build` (auto-detected)
   - **Start command**: `npm start` (auto-detected)

### Step 9: Configure Frontend Environment Variables
Go to Frontend service → Variables tab dan add:

```env
# API Configuration (gunakan backend URL dari step 7)
NEXT_PUBLIC_API_URL=https://backend-api-production.up.railway.app

# App Configuration
NEXT_PUBLIC_APP_NAME=Posyandu Digital
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### Step 10: Deploy Frontend
1. Click "Deploy" atau wait for auto-deploy
2. Monitor logs untuk memastikan build berhasil
3. Copy frontend service URL (format: `https://frontend-web-production.up.railway.app`)

## 🔄 Phase 5: Update Cross-Service URLs

### Step 11: Update Backend FRONTEND_URL
1. Go to Backend service → Variables
2. Update `FRONTEND_URL` dengan frontend URL dari step 10
3. Redeploy backend service

### Step 12: Verify All Services
Check semua services running:
- ✅ MySQL Database: Status "Active"
- ✅ Backend API: Status "Active" 
- ✅ Frontend Web: Status "Active"

## 📊 Phase 6: Database Migration

### Step 13: Import Database Schema
1. Use database credentials dari step 4
2. Connect menggunakan MySQL client
3. Run migration script:
   ```bash
   mysql -h <MYSQL_HOST> -P <MYSQL_PORT> -u <MYSQL_USER> -p <MYSQL_DATABASE> < database/migrate-to-railway.sql
   ```

### Step 14: Test Database Connection
```bash
# Set environment variables
export DB_HOST=<MYSQL_HOST>
export DB_USER=<MYSQL_USER>
export DB_PASSWORD=<MYSQL_PASSWORD>
export DB_NAME=<MYSQL_DATABASE>
export DB_PORT=<MYSQL_PORT>

# Test connection
node database/test-connection.js
```

## ✅ Phase 7: Final Verification

### Step 15: Test All Endpoints

#### Backend Health Check:
```bash
curl https://your-backend-url.up.railway.app/health
```

Expected response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-XX...",
  "uptime": 123.45
}
```

#### Frontend Access:
1. Open frontend URL di browser
2. Test QR code scanning functionality
3. Test profile creation/viewing
4. Test checkup recording

### Step 16: Monitor Logs
1. Check backend logs untuk database connections
2. Check frontend logs untuk API calls
3. Verify no errors in Railway dashboard

## 🎯 Success Criteria

- [ ] All 3 services deployed successfully
- [ ] Database schema imported
- [ ] Backend API responding to health checks
- [ ] Frontend loading properly
- [ ] API calls working between frontend-backend
- [ ] Database queries working
- [ ] QR code functionality working
- [ ] No errors in service logs

## 📞 Troubleshooting

### Common Issues:

1. **Build Failures**: Check Node.js version, dependencies
2. **Database Connection**: Verify credentials, SSL settings
3. **CORS Errors**: Check FRONTEND_URL configuration
4. **Environment Variables**: Verify variable names and values

### Debug Commands:
```bash
# Check service logs
railway logs --service backend-api
railway logs --service frontend-web
railway logs --service mysql-database

# Connect to database
railway connect mysql-database
```

## 🎉 Congratulations!

Jika semua steps berhasil, aplikasi Posyandu Digital sudah running di Railway! 

**URLs:**
- Frontend: `https://frontend-web-production.up.railway.app`
- Backend API: `https://backend-api-production.up.railway.app`
- Database: Internal Railway MySQL

Next: Test semua functionality dan monitor performance.
