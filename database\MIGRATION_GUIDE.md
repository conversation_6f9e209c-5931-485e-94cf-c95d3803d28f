# 🗄️ Railway Database Migration Guide

## 📋 Overview

Panduan untuk migrate database schema dan data ke Railway MySQL service.

## 🔧 Prerequisites

1. ✅ Railway MySQL service sudah running
2. ✅ Database credentials tersedia di Railway dashboard
3. ✅ MySQL client installed (MySQL Workbench, phpMyAdmin, atau CLI)

## 📊 Migration Files

```
database/
├── migrate-to-railway.sql    # Main migration script
├── test-connection.js        # Connection test script
└── MIGRATION_GUIDE.md       # This guide
```

## 🚀 Migration Steps

### Step 1: Get Railway Database Credentials

1. Login ke Railway dashboard
2. Go to MySQL service
3. Click "Variables" tab
4. Copy these values:
   - `MYSQL_HOST`
   - `MYSQL_USER` 
   - `MYSQL_PASSWORD`
   - `MYSQL_DATABASE` (usually 'railway')
   - `MYSQL_PORT`

### Step 2: Test Connection

```bash
# Set environment variables
export DB_HOST=your-mysql-host
export DB_USER=your-mysql-user
export DB_PASSWORD=your-mysql-password
export DB_NAME=railway
export DB_PORT=your-mysql-port

# Test connection
cd server
node ../database/test-connection.js
```

### Step 3: Run Migration Script

#### Option A: Using MySQL CLI
```bash
mysql -h your-mysql-host -P your-mysql-port -u your-mysql-user -p railway < database/migrate-to-railway.sql
```

#### Option B: Using MySQL Workbench
1. Open MySQL Workbench
2. Create new connection with Railway credentials
3. Open `database/migrate-to-railway.sql`
4. Execute the script

#### Option C: Using Railway CLI (if available)
```bash
railway connect mysql
# Then paste the SQL content
```

### Step 4: Verify Migration

```bash
# Run connection test again
node database/test-connection.js
```

Expected output:
```
✅ Basic connection successful!
✅ Database query successful!
✅ Tables found:
   - profiles
   - checkups
✅ Profiles table: 5 records
✅ Checkups table: 10 records
```

## 📊 Database Schema

### Tables Created:

#### `profiles` table:
- `id` (Primary Key, Auto Increment)
- `email` (VARCHAR 255, Indexed)
- `nama` (VARCHAR 100, NOT NULL)
- `usia` (INT, NOT NULL)
- `alamat` (TEXT, NOT NULL)
- `riwayat_medis` (TEXT)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### `checkups` table:
- `id` (Primary Key, Auto Increment)
- `profile_id` (Foreign Key to profiles.id)
- `tekanan_darah` (VARCHAR 20)
- `gula_darah` (INT)
- `tanggal` (DATETIME)
- `catatan` (TEXT)

### Sample Data:
- 5 sample profiles
- 10 sample checkup records
- Realistic healthcare data for testing

## 🔍 Troubleshooting

### Common Issues:

#### 1. Connection Refused
```
Error: connect ECONNREFUSED
```
**Solution**: Check Railway MySQL service is running and credentials are correct.

#### 2. SSL Connection Error
```
Error: ER_NOT_SUPPORTED_AUTH_MODE
```
**Solution**: Add SSL configuration or use legacy authentication.

#### 3. Database Not Found
```
Error: Unknown database 'railway'
```
**Solution**: Use the exact database name from Railway variables.

#### 4. Permission Denied
```
Error: Access denied for user
```
**Solution**: Verify username and password from Railway dashboard.

### Debug Commands:

```bash
# Check Railway service status
railway status

# View MySQL service logs
railway logs --service mysql

# Connect to Railway MySQL directly
railway connect mysql
```

## ✅ Verification Checklist

- [ ] Railway MySQL service running
- [ ] Connection test passes
- [ ] Tables created successfully
- [ ] Sample data inserted
- [ ] Foreign key constraints working
- [ ] Indexes created properly
- [ ] Backend can connect to database

## 🎯 Next Steps

After successful migration:

1. ✅ Database migrated
2. ⏳ Update backend environment variables
3. ⏳ Deploy backend service
4. ⏳ Test API endpoints
5. ⏳ Deploy frontend service

## 📞 Support

If migration fails:
1. Check Railway service logs
2. Verify all credentials
3. Test connection manually
4. Check firewall/network settings
5. Contact Railway support if needed
