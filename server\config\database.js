const mysql = require('mysql2/promise');
require('dotenv').config();

// Konfigurasi koneksi database
const dbConfig = {
    host: process.env.DB_HOST || 'fe_lansia-project.railway.internal',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'railway',
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true
};

// Membuat connection pool
const pool = mysql.createPool(dbConfig);

// Test koneksi database
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Database connected successfully');
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
}

// Export pool dan test function
module.exports = {
    pool,
    testConnection
};
