#!/usr/bin/env node

/**
 * Railway Database Connection Test Script
 * 
 * This script tests the database connection using Railway environment variables
 * Run this after setting up Railway MySQL service
 * 
 * Usage:
 * node database/test-connection.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Railway database configuration
const dbConfig = {
    host: process.env.DB_HOST || process.env.MYSQL_HOST,
    user: process.env.DB_USER || process.env.MYSQL_USER,
    password: process.env.DB_PASSWORD || process.env.MYSQL_PASSWORD,
    database: process.env.DB_NAME || process.env.MYSQL_DATABASE || 'railway',
    port: process.env.DB_PORT || process.env.MYSQL_PORT || 3306,
    ssl: {
        rejectUnauthorized: false // Railway requires SSL
    }
};

async function testConnection() {
    console.log('🔄 Testing Railway MySQL connection...');
    console.log('📊 Configuration:');
    console.log(`   Host: ${dbConfig.host}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Database: ${dbConfig.database}`);
    console.log(`   Port: ${dbConfig.port}`);
    console.log('');

    try {
        // Test basic connection
        console.log('1️⃣ Testing basic connection...');
        const connection = await mysql.createConnection(dbConfig);
        console.log('✅ Basic connection successful!');

        // Test database query
        console.log('2️⃣ Testing database query...');
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ Database query successful!', rows);

        // Test tables existence
        console.log('3️⃣ Checking tables...');
        const [tables] = await connection.execute('SHOW TABLES');
        
        if (tables.length === 0) {
            console.log('⚠️  No tables found. Run migration script first.');
        } else {
            console.log('✅ Tables found:');
            tables.forEach(table => {
                const tableName = Object.values(table)[0];
                console.log(`   - ${tableName}`);
            });
        }

        // Test profiles table if exists
        try {
            console.log('4️⃣ Testing profiles table...');
            const [profiles] = await connection.execute('SELECT COUNT(*) as count FROM profiles');
            console.log(`✅ Profiles table: ${profiles[0].count} records`);
        } catch (error) {
            console.log('⚠️  Profiles table not found or empty');
        }

        // Test checkups table if exists
        try {
            console.log('5️⃣ Testing checkups table...');
            const [checkups] = await connection.execute('SELECT COUNT(*) as count FROM checkups');
            console.log(`✅ Checkups table: ${checkups[0].count} records`);
        } catch (error) {
            console.log('⚠️  Checkups table not found or empty');
        }

        await connection.end();
        console.log('');
        console.log('🎉 All tests completed successfully!');
        console.log('✅ Railway MySQL connection is working properly');

    } catch (error) {
        console.error('❌ Connection test failed:');
        console.error('Error:', error.message);
        console.error('');
        console.error('🔧 Troubleshooting:');
        console.error('1. Check Railway MySQL service is running');
        console.error('2. Verify environment variables are set correctly');
        console.error('3. Ensure SSL connection is allowed');
        console.error('4. Check Railway service logs for more details');
        
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testConnection().catch(console.error);
}

module.exports = { testConnection, dbConfig };
